#!/usr/bin/env python3
"""
易经八卦知识库MCP服务器
专门处理易经相关的查询和分析
"""

import json
import sqlite3
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

# MCP相关导入 - 使用正确的导入方式
from mcp.server import Server
from mcp.types import Tool, TextContent
import mcp.server.stdio

class YijingKnowledgeServer:
    def __init__(self):
        self.db_path = os.getenv("YIJING_DB_PATH", "/data/yijing/knowledge.db")
        self.language = os.getenv("LANGUAGE", "zh-CN")

        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        self.server = Server("yijing-knowledge")
        self.init_database()
        self.setup_tools()

    def setup_tools(self):
        """设置MCP工具"""

        @self.server.tool()
        async def hexagram_query(query: str, method: str = "name") -> str:
            """
            查询六十四卦信息

            Args:
                query: 查询内容（卦名、卦号等）
                method: 查询方式 ("name", "number", "situation")
            """
            result = self._query_hexagram_impl(query, method)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.server.tool()
        async def divination_analysis(question: str, hexagram_number: int) -> str:
            """
            占卜分析

            Args:
                question: 占卜问题
                hexagram_number: 卦象编号
            """
            result = self._divination_analysis_impl(question, hexagram_number)
            return json.dumps(result, ensure_ascii=False, indent=2)

    def _query_hexagram_impl(self, query: str, method: str) -> Dict[str, Any]:
        """查询卦象的实现"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            if method == "name":
                cursor.execute('''
                    SELECT * FROM hexagrams WHERE name_chinese = ? OR name_pinyin = ?
                ''', (query, query))
            elif method == "number":
                cursor.execute('''
                    SELECT * FROM hexagrams WHERE number = ?
                ''', (int(query),))
            else:
                cursor.execute('''
                    SELECT * FROM hexagrams WHERE interpretation LIKE ?
                ''', (f'%{query}%',))

            result = cursor.fetchone()
            if result:
                return {
                    "卦号": result[1],
                    "卦名": result[2],
                    "拼音": result[3],
                    "上卦": result[4],
                    "下卦": result[5],
                    "卦辞": result[6],
                    "象辞": result[7],
                    "解释": result[8],
                    "现代应用": result[9]
                }
            else:
                return {"错误": "未找到相关卦象"}
        finally:
            conn.close()

    def _divination_analysis_impl(self, question: str, hexagram_number: int) -> Dict[str, Any]:
        """占卜分析的实现"""
        hexagram_result = self._query_hexagram_impl(str(hexagram_number), "number")

        if "错误" in hexagram_result:
            return hexagram_result

        return {
            "问题": question,
            "卦象": hexagram_result,
            "主卦分析": self._analyze_main_hexagram(hexagram_result),
            "时运分析": self._analyze_timing(hexagram_result),
            "建议": self._generate_advice(question, hexagram_result),
            "注意事项": self._generate_warnings(hexagram_result)
        }

    def init_database(self):
        """初始化易经知识数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建六十四卦表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hexagrams (
                id INTEGER PRIMARY KEY,
                number INTEGER UNIQUE,
                name_chinese TEXT,
                name_pinyin TEXT,
                upper_trigram TEXT,
                lower_trigram TEXT,
                judgment TEXT,
                image TEXT,
                interpretation TEXT,
                modern_application TEXT
            )
        ''')
        
        # 创建爻辞表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS yao_lines (
                id INTEGER PRIMARY KEY,
                hexagram_id INTEGER,
                line_number INTEGER,
                line_text TEXT,
                interpretation TEXT,
                FOREIGN KEY (hexagram_id) REFERENCES hexagrams (id)
            )
        ''')
        
        # 创建八卦基础表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trigrams (
                id INTEGER PRIMARY KEY,
                name_chinese TEXT,
                name_pinyin TEXT,
                symbol TEXT,
                element TEXT,
                direction TEXT,
                family_member TEXT,
                attributes TEXT,
                meaning TEXT
            )
        ''')
        
        conn.commit()
        conn.close()

    @Tool("hexagram_query")
    async def query_hexagram(self, query: str, method: str = "name") -> Dict[str, Any]:
        """
        查询六十四卦信息
        
        Args:
            query: 查询内容（卦名、卦号等）
            method: 查询方式 ("name", "number", "situation")
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if method == "name":
            cursor.execute('''
                SELECT * FROM hexagrams 
                WHERE name_chinese LIKE ? OR name_pinyin LIKE ?
            ''', (f'%{query}%', f'%{query}%'))
        elif method == "number":
            cursor.execute('SELECT * FROM hexagrams WHERE number = ?', (int(query),))
        elif method == "situation":
            cursor.execute('''
                SELECT * FROM hexagrams 
                WHERE interpretation LIKE ? OR modern_application LIKE ?
            ''', (f'%{query}%', f'%{query}%'))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            hexagram_data = {
                "卦号": result[1],
                "卦名": result[2],
                "拼音": result[3], 
                "上卦": result[4],
                "下卦": result[5],
                "卦辞": result[6],
                "象辞": result[7],
                "解释": result[8],
                "现代应用": result[9]
            }
            
            # 获取爻辞
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT line_number, line_text, interpretation 
                FROM yao_lines WHERE hexagram_id = ?
                ORDER BY line_number
            ''', (result[0],))
            yao_lines = cursor.fetchall()
            conn.close()
            
            hexagram_data["爻辞"] = [
                {
                    "爻位": line[0],
                    "爻辞": line[1], 
                    "解释": line[2]
                } for line in yao_lines
            ]
            
            return hexagram_data
        else:
            return {"错误": "未找到相关卦象"}

    @Tool("divination_analysis")
    async def analyze_divination(self, question: str, hexagram_result: Dict) -> Dict[str, Any]:
        """
        分析占卜结果
        
        Args:
            question: 占卜问题
            hexagram_result: 卦象结果
        """
        analysis = {
            "问题": question,
            "主卦分析": self._analyze_main_hexagram(hexagram_result),
            "时运分析": self._analyze_timing(hexagram_result),
            "建议": self._generate_advice(question, hexagram_result),
            "注意事项": self._generate_warnings(hexagram_result)
        }
        
        return analysis

    @Tool("yao_interpretation")
    async def interpret_changing_lines(self, hexagram_id: int, changing_lines: List[int]) -> Dict[str, Any]:
        """
        解释变爻
        
        Args:
            hexagram_id: 卦象ID
            changing_lines: 变爻位置列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        interpretations = []
        for line_num in changing_lines:
            cursor.execute('''
                SELECT line_text, interpretation 
                FROM yao_lines 
                WHERE hexagram_id = ? AND line_number = ?
            ''', (hexagram_id, line_num))
            
            result = cursor.fetchone()
            if result:
                interpretations.append({
                    "爻位": f"第{line_num}爻",
                    "爻辞": result[0],
                    "解释": result[1],
                    "变化意义": self._analyze_line_change(line_num, result[0])
                })
        
        conn.close()
        
        return {
            "变爻分析": interpretations,
            "整体变化": self._analyze_overall_change(changing_lines),
            "行动指导": self._generate_action_guidance(changing_lines)
        }

    @Tool("philosophical_guidance")
    async def provide_philosophical_guidance(self, life_situation: str) -> Dict[str, Any]:
        """
        基于易经哲学提供人生指导
        
        Args:
            life_situation: 人生处境描述
        """
        # 分析情况并匹配相应的易经智慧
        relevant_concepts = self._match_yijing_concepts(life_situation)
        
        guidance = {
            "情况分析": self._analyze_life_situation(life_situation),
            "相关易理": relevant_concepts,
            "哲学指导": self._generate_philosophical_advice(life_situation, relevant_concepts),
            "实践建议": self._generate_practical_advice(life_situation),
            "相关卦象": self._suggest_relevant_hexagrams(life_situation)
        }
        
        return guidance

    def _analyze_main_hexagram(self, hexagram_result: Dict) -> str:
        """分析主卦含义"""
        return f"主卦{hexagram_result.get('卦名', '')}，象征{hexagram_result.get('解释', '')}"

    def _analyze_timing(self, hexagram_result: Dict) -> str:
        """分析时运"""
        # 基于卦象分析当前时机
        return "根据卦象显示，当前时机..."

    def _generate_advice(self, question: str, hexagram_result: Dict) -> List[str]:
        """生成建议"""
        return [
            "保持谦逊，顺应自然",
            "把握时机，适时而动", 
            "注重内在修养"
        ]

    def _generate_warnings(self, hexagram_result: Dict) -> List[str]:
        """生成注意事项"""
        return [
            "避免急躁冒进",
            "注意人际关系",
            "保持内心平静"
        ]

    def _analyze_line_change(self, line_num: int, line_text: str) -> str:
        """分析爻变意义"""
        return f"第{line_num}爻变化表示..."

    def _analyze_overall_change(self, changing_lines: List[int]) -> str:
        """分析整体变化"""
        return f"共有{len(changing_lines)}个变爻，表示..."

    def _generate_action_guidance(self, changing_lines: List[int]) -> List[str]:
        """生成行动指导"""
        return ["根据变爻指示，建议..."]

    def _match_yijing_concepts(self, situation: str) -> List[Dict[str, str]]:
        """匹配易经概念"""
        return [
            {"概念": "阴阳平衡", "说明": "万物皆有阴阳两面"},
            {"概念": "变化规律", "说明": "易者，变也"}
        ]

    def _analyze_life_situation(self, situation: str) -> str:
        """分析人生处境"""
        return f"根据您描述的情况：{situation}，从易经角度分析..."

    def _generate_philosophical_advice(self, situation: str, concepts: List[Dict]) -> List[str]:
        """生成哲学建议"""
        return [
            "顺应自然规律，不强求",
            "保持中庸之道",
            "以德化人，以和为贵"
        ]

    def _generate_practical_advice(self, situation: str) -> List[str]:
        """生成实践建议"""
        return [
            "在日常生活中保持平和心态",
            "多读经典，增长智慧",
            "与人为善，积德行善"
        ]

    def _suggest_relevant_hexagrams(self, situation: str) -> List[Dict[str, str]]:
        """推荐相关卦象"""
        return [
            {"卦名": "乾卦", "建议": "自强不息"},
            {"卦名": "坤卦", "建议": "厚德载物"}
        ]

def main():
    """主函数"""
    server_instance = YijingKnowledgeServer()

    try:
        # 使用标准MCP服务器运行
        import mcp.server.stdio
        mcp.server.stdio.run_server(server_instance.server)
    except ImportError:
        # 简单的测试模式
        print("MCP库未安装，运行测试模式")
        print("可用工具:", list(server_instance.server.tools.keys()) if hasattr(server_instance.server, 'tools') else [])

if __name__ == "__main__":
    main()
