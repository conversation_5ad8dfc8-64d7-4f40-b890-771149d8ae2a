{"mcpServers": {"yijing-knowledge": {"command": "python", "args": ["-m", "mcp_servers.yijing_server"], "env": {"YIJING_DB_PATH": "/data/yijing/knowledge.db", "LANGUAGE": "zh-CN"}, "description": "易经八卦知识库服务器", "capabilities": ["hexagram_query", "divination_analysis", "yao_interpretation", "philosophical_guidance"]}, "fengshui-analysis": {"command": "python", "args": ["-m", "mcp_servers.fengshui_server"], "env": {"FENGSHUI_RULES_PATH": "/data/fengshui/rules.json", "COMPASS_DATA": "/data/fengshui/compass.db", "LANGUAGE": "zh-CN"}, "description": "风水堪舆分析服务器", "capabilities": ["house_analysis", "direction_analysis", "wuxing_calculation", "layout_suggestion"]}, "tcm-knowledge": {"command": "python", "args": ["-m", "mcp_servers.tcm_server"], "env": {"TCM_HERB_DB": "/data/tcm/herbs.db", "FORMULA_DB": "/data/tcm/formulas.db", "SYNDROME_DB": "/data/tcm/syndromes.db", "LANGUAGE": "zh-CN"}, "description": "中医药典知识服务器", "capabilities": ["herb_query", "formula_analysis", "syndrome_diagnosis", "constitution_analysis"]}, "intangible-heritage": {"command": "python", "args": ["-m", "mcp_servers.heritage_server"], "env": {"HERITAGE_DB": "/data/heritage/unesco_china.db", "CRAFT_DB": "/data/heritage/traditional_crafts.db", "FESTIVAL_DB": "/data/heritage/festivals.db", "LANGUAGE": "zh-CN"}, "description": "非物质文化遗产服务器", "capabilities": ["heritage_query", "craft_tutorial", "festival_explanation", "cultural_context"]}, "chinese-calendar": {"command": "python", "args": ["-m", "mcp_servers.calendar_server"], "env": {"CALENDAR_DB": "/data/calendar/lunar_solar.db", "ALMANAC_DB": "/data/calendar/almanac.db", "LANGUAGE": "zh-CN"}, "description": "中国传统历法服务器", "capabilities": ["lunar_calendar", "solar_terms", "auspicious_dates", "zodiac_analysis"]}, "classical-literature": {"command": "python", "args": ["-m", "mcp_servers.literature_server"], "env": {"CLASSICS_DB": "/data/literature/classics.db", "POETRY_DB": "/data/literature/poetry.db", "PHILOSOPHY_DB": "/data/literature/philosophy.db", "LANGUAGE": "zh-CN"}, "description": "中国古典文学服务器", "capabilities": ["classic_query", "poetry_analysis", "philosophical_interpretation", "historical_context"]}, "geomancy-tools": {"command": "python", "args": ["-m", "mcp_servers.geomancy_server"], "env": {"LUOPAN_DATA": "/data/geomancy/luopan.json", "MOUNTAIN_WATER": "/data/geomancy/landscape.db", "LANGUAGE": "zh-CN"}, "description": "堪舆工具服务器", "capabilities": ["luopan_reading", "landscape_analysis", "qi_flow_calculation", "site_evaluation"]}, "numerology-bazi": {"command": "python", "args": ["-m", "mcp_servers.bazi_server"], "env": {"BAZI_RULES": "/data/bazi/calculation_rules.json", "WUXING_DATA": "/data/bazi/wuxing_relations.db", "LANGUAGE": "zh-CN"}, "description": "八字命理服务器", "capabilities": ["bazi_calculation", "destiny_analysis", "compatibility_check", "fortune_prediction"]}, "traditional-medicine-diagnosis": {"command": "python", "args": ["-m", "mcp_servers.tcm_diagnosis_server"], "env": {"SYMPTOM_DB": "/data/tcm/symptoms.db", "PULSE_PATTERNS": "/data/tcm/pulse_diagnosis.json", "TONGUE_DIAGNOSIS": "/data/tcm/tongue_patterns.db", "LANGUAGE": "zh-CN"}, "description": "中医诊断辅助服务器", "capabilities": ["symptom_analysis", "pulse_diagnosis", "tongue_diagnosis", "treatment_suggestion"]}, "cultural-context": {"command": "python", "args": ["-m", "mcp_servers.cultural_context_server"], "env": {"CULTURAL_DB": "/data/culture/context.db", "REGIONAL_DATA": "/data/culture/regional_customs.json", "LANGUAGE": "zh-CN"}, "description": "文化背景解释服务器", "capabilities": ["cultural_explanation", "historical_background", "regional_variations", "modern_applications"]}}, "knowledgeBases": {"yijing": {"path": "/data/knowledge/yijing/", "format": "structured_json", "encoding": "utf-8", "sources": ["周易原文", "象传", "彖传", "文言传", "系辞传", "说卦传", "序卦传", "杂卦传"]}, "fengshui": {"path": "/data/knowledge/fengshui/", "format": "structured_json", "encoding": "utf-8", "sources": ["玄空风水", "八宅风水", "三合风水", "飞星风水", "形势风水"]}, "tcm": {"path": "/data/knowledge/tcm/", "format": "structured_json", "encoding": "utf-8", "sources": ["本草纲目", "伤寒论", "金匮要略", "黄帝内经", "温病条辨"]}, "heritage": {"path": "/data/knowledge/heritage/", "format": "structured_json", "encoding": "utf-8", "sources": ["UNESCO非遗名录", "国家级非遗项目", "省级非遗项目", "传统工艺", "民俗文化"]}}, "aiModelConfig": {"primaryModel": {"provider": "groq", "model": "llama-3.3-70b-versatile", "specialization": "中文理解和传统文化", "systemPrompt": "你是一位精通中国传统文化的AI助手，专门帮助用户学习和理解易经、风水、中医、非遗等传统知识。请用专业而易懂的中文回答问题。"}, "backupModel": {"provider": "deepseek", "model": "deepseek-coder", "specialization": "中文语境和文化理解", "systemPrompt": "作为中国传统文化专家，请提供准确、专业的传统文化知识解答。"}}, "userInterface": {"defaultLanguage": "zh-CN", "culturalTheme": "traditional-chinese", "colorScheme": {"primary": "#8B4513", "secondary": "#DAA520", "accent": "#DC143C", "background": "#FFF8DC"}, "fonts": {"primary": "思源宋体", "secondary": "思源黑体"}}, "dataSourcePriority": ["authoritative_classics", "academic_research", "traditional_masters", "modern_applications", "user_contributions"]}