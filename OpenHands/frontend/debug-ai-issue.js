const { chromium } = require('playwright');

async function debugAIIssue() {
  console.log('🔍 启动Playwright调试...');
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    console.log(`🖥️  Console ${msg.type()}: ${msg.text()}`);
  });
  
  // 监听网络请求
  page.on('request', request => {
    if (request.url().includes('/api/') || request.url().includes('/socket.io/')) {
      console.log(`📡 Request: ${request.method()} ${request.url()}`);
    }
  });
  
  // 监听网络响应
  page.on('response', response => {
    if (response.url().includes('/api/') || response.url().includes('/socket.io/')) {
      console.log(`📨 Response: ${response.status()} ${response.url()}`);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log(`❌ Page Error: ${error.message}`);
  });
  
  try {
    console.log('🌐 访问页面...');
    await page.goto('https://ai.guiyunai.fun/conversations/468355a7e82a46f2b1bbba6492a007ca', {
      waitUntil: 'networkidle',
      timeout: 30000
    });
    
    console.log('⏳ 等待页面加载...');
    await page.waitForTimeout(5000);
    
    // 检查页面标题
    const title = await page.title();
    console.log(`📄 页面标题: ${title}`);
    
    // 检查是否有错误消息
    const errorElements = await page.$$('[data-testid*="error"], .error, [class*="error"]');
    if (errorElements.length > 0) {
      console.log(`❌ 发现 ${errorElements.length} 个错误元素`);
      for (let i = 0; i < errorElements.length; i++) {
        const text = await errorElements[i].textContent();
        console.log(`   错误 ${i + 1}: ${text}`);
      }
    }
    
    // 检查是否有加载指示器
    const loadingElements = await page.$$('[data-testid*="loading"], .loading, [class*="loading"], [class*="spinner"]');
    if (loadingElements.length > 0) {
      console.log(`⏳ 发现 ${loadingElements.length} 个加载元素`);
      for (let i = 0; i < loadingElements.length; i++) {
        const text = await loadingElements[i].textContent();
        console.log(`   加载 ${i + 1}: ${text}`);
      }
    }
    
    // 检查WebSocket连接状态
    const wsStatus = await page.evaluate(() => {
      return {
        readyState: window.WebSocket ? 'WebSocket available' : 'WebSocket not available',
        socketConnections: window.io ? 'Socket.IO available' : 'Socket.IO not available'
      };
    });
    console.log(`🔌 WebSocket状态:`, wsStatus);
    
    // 检查是否有AI相关的按钮或输入框
    const aiElements = await page.$$('input[placeholder*="message"], textarea[placeholder*="message"], button[data-testid*="send"]');
    console.log(`🤖 发现 ${aiElements.length} 个AI交互元素`);
    
    // 尝试查找具体的错误信息
    const bodyText = await page.textContent('body');
    if (bodyText.includes('等待运行时启动')) {
      console.log('⚠️  发现"等待运行时启动"消息');
    }
    if (bodyText.includes('502')) {
      console.log('❌ 发现502错误');
    }
    if (bodyText.includes('WebSocket')) {
      console.log('🔌 页面包含WebSocket相关内容');
    }
    
    console.log('✅ 调试完成，保持浏览器打开以便进一步检查...');
    
    // 保持浏览器打开一段时间
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.log(`❌ 调试过程中出错: ${error.message}`);
  } finally {
    await browser.close();
  }
}

debugAIIssue().catch(console.error);
